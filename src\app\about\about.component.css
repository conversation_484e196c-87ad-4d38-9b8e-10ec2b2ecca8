/* Modern About Component Styles */

.about-me {
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  background: linear-gradient(135deg,
    var(--primary-pink-lightest) 0%,
    rgba(255, 255, 255, 0.95) 50%,
    var(--rose-lighter) 100%);
  transition: background 0.3s ease;
}

/* Dark mode background */
:host-context(.dark) .about-me {
  background: linear-gradient(135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 50%,
    var(--bg-tertiary) 100%);
}

.about-me .about-me-container {
  position: relative;
  z-index: 2;
}

@media (max-width: 960px) {
  .about-me .about-me-container {
    padding-bottom: 60px;
  }
}
/* Modern Header Design */
.about-me-header {
  position: relative;
  z-index: 10;
  padding: 40px 0 30px 0;
  text-align: center;

}

.about-me-header .about-me-title {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-pink), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.1;
  margin: 0;
  letter-spacing: -0.02em;
  position: relative;
  transition: all 0.3s ease;
}

/* Dark mode title styling */
:host-context(.dark) .about-me-header .about-me-title {
  background: linear-gradient(135deg, var(--primary-pink), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.about-me-header .about-me-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-pink), var(--accent-pink));
  border-radius: 2px;
}

/* Responsive header styles */
@media (max-width: 768px) {
  .about-me-header {
    padding: 30px 0 20px 0;
  }

  .about-me-header .about-me-title {
    font-size: 3rem;
  }
}

@media (max-width: 500px) {
  .about-me-header {
    padding: 25px 0 15px 0;
  }

  .about-me-header .about-me-title {
    font-size: 2.5rem;
  }
}

/* Modern Content Layout */
.about-me-flex-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 40px;
  text-align: right;
  padding: 20px;
}

@media (max-width: 768px) {
  .about-me-flex-container {
    gap: 30px;
    padding: 15px;
  }
}

@media (max-width: 500px) {
  .about-me-flex-container {
    gap: 25px;
    padding: 10px;
  }
}
/* Removed unused image styles - using Spline animation instead */
/* Enhanced Content Container */
.about-me-flex-container .about-me-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 40px;
  width: 100%;
  max-width: 900px;
  background: rgba(255, 255, 255, 0.7);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow:
    0 8px 32px rgba(219, 39, 119, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid var(--border-pink);
  transition: all 0.3s ease;
}

/* Dark mode content styling */
:host-context(.dark) .about-me-flex-container .about-me-content {
  background: rgba(30, 30, 63, 0.8);
  border: 1px solid var(--border-default);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(244, 114, 182, 0.1);
}

@media (max-width: 768px) {
  .about-me-flex-container .about-me-content {
    gap: 30px;
    max-width: 100%;
    padding: 30px;
    border-radius: 15px;
  }
}

@media (max-width: 500px) {
  .about-me-flex-container .about-me-content {
    padding: 20px;
    gap: 25px;
  }
}
/* Removed unused logo styles */
/* Enhanced Text Styling */
.about-me-flex-container .about-me-content .text {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1.25rem; /* 20px */
  line-height: 1.8;
  text-align: right;
  max-width: 100%;
  margin: 0;
  letter-spacing: 0.01em;
  position: relative;
  transition: color 0.3s ease;
}

/* Dark mode text styling */
:host-context(.dark) .about-me-flex-container .about-me-content .text {
  color: var(--text-on-bg);
}

/* .about-me-flex-container .about-me-content .text::before {
  content: '';
  position: absolute;
  top: -10px;
  right: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-pink), var(--primary-pink));
  border-radius: 2px;
} */

@media (max-width: 768px) {
  .about-me-flex-container .about-me-content .text {
    font-size: 1.125rem; /* 18px */
    line-height: 1.7;
  }
}

@media (max-width: 500px) {
  .about-me-flex-container .about-me-content .text {
    font-size: 1rem; /* 16px */
    line-height: 1.6;
  }
}


/* Removed unused spline-viewer styles - using iframe instead */

/* Button Text and Icon Styling */
.cta span {
  position: relative;
  font-size: 1.125rem; /* 18px */
  font-weight: 600;
  letter-spacing: 0.02em;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.cta svg {
  position: relative;
  margin-left: 12px;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke: white;
  stroke-width: 2.5;
  transform: translateX(-3px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
}

.cta:hover svg {
  transform: translateX(3px);
  stroke-width: 3;
}

@media (max-width: 768px) {
  .cta {
    padding: 16px 35px;
  }

  .cta span {
    font-size: 1rem;
  }
}

@media (max-width: 500px) {
  .cta {
    padding: 14px 30px;
  }

  .cta span {
    font-size: 0.9rem;
  }
}
/* Removed duplicate and unused image styles */

/* Modern Button Design */
.cta {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 18px 40px;
  background: linear-gradient(135deg, var(--primary-pink), var(--accent-pink));
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  box-shadow:
    0 8px 25px rgba(219, 39, 119, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* Dark mode button styling */
:host-context(.dark) .cta {
  background: linear-gradient(135deg, var(--primary-pink), var(--accent-pink));
  box-shadow:
    0 8px 25px rgba(244, 114, 182, 0.4),
    0 0 0 1px rgba(244, 114, 182, 0.2);
}

:host-context(.dark) .cta:hover {
  box-shadow:
    0 12px 35px rgba(244, 114, 182, 0.5),
    0 0 0 1px rgba(244, 114, 182, 0.3);
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.cta:hover {
  background: linear-gradient(135deg, var(--accent-pink), var(--primary-pink));
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 35px rgba(219, 39, 119, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.cta:hover::before {
  left: 100%;
}

.cta:active {
  transform: translateY(-1px) scale(0.98);
}

/* Duplicate text style removed - handled in .about-me-flex-container .about-me-content .text */
/* Modern Layout Design */
.about-me-layout {
  display: flex;
  min-height: 100vh;
  align-items: center;
  position: relative;
}

/* Content on the right */
.content-right {
  flex: 1;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-height: 100vh;
  order: 2;
  z-index: 2;
}

/* Spline animation on the left */
.spline-left-wrapper {
  flex: 0 0 50%;
  height: 100vh;
  position: relative;
  order: 1;
  z-index: 1;
}

.spline-left-wrapper iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
  border-radius: 0 20px 20px 0;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .about-me-layout {
    flex-direction: column;
    min-height: 100vh;
  }

  .spline-left-wrapper {
    flex: none;
    width: 100%;
    height: 400px;
    order: 1;
  }

  .spline-left-wrapper iframe {
    border-radius: 0;
  }

  .content-right {
    padding: 40px 30px;
    min-height: 60vh;
    order: 2;
  }
}

@media (max-width: 768px) {
  .spline-left-wrapper {
    height: 350px;
  }

  .content-right {
    padding: 30px 20px;
    min-height: 50vh;
  }

  .about-me-flex-container .about-me-content {
    padding: 25px;
  }
}

@media (max-width: 480px) {
  .spline-left-wrapper {
    height: 300px;
  }

  .content-right {
    padding: 20px 15px;
    min-height: 40vh;
  }

  .about-me-flex-container .about-me-content {
    padding: 20px;
    border-radius: 12px;
  }

  .about-me-header .about-me-title {
    font-size: 2rem;
  }
}

/* Additional modern enhancements */
@media (prefers-reduced-motion: reduce) {
  .cta,
  .cta::before,
  .cta svg,
  .about-me-header .about-me-title {
    transition: none;
  }
}

/* Smooth scrolling for better UX */
.about-me {
  scroll-behavior: smooth;
}
