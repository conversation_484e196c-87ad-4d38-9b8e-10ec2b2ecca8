/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}"
  ],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'sans-serif'],
        'primary': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'sans-serif'],
        'montserrat': ['Montserrat', 'sans-serif'],
        'nunito': ['Nunito', 'sans-serif'],
        'pacifico': ['Pacifico', 'cursive'],
        'ubuntu': ['Ubuntu', 'sans-serif'],
      },
      colors: {
        'primary-pink': {
          DEFAULT: '#db2777', // pink-600
          'dark': '#be185d', // pink-700
          'light': '#f9a8d4', // pink-300
          'lighter': '#fce7f3', // pink-100
          'lightest': '#fdf2f8', // pink-50
        },
        'accent-pink': '#f472b6', // pink-400
        'border-pink': {
          DEFAULT: '#fbcfe8', // pink-200
          'dark': '#f9a8d4', // pink-300
        },
        'text-primary': '#db2777',
        'text-secondary': '#be185d',
        // Dark mode specific colors
        'dark': {
          'bg': {
            'primary': '#0f0f23', // Very dark blue-purple
            'secondary': '#1a1a2e', // Dark blue-purple
            'tertiary': '#16213e', // Medium dark blue
            'card': '#1e1e3f', // Card background
            'hover': '#252550', // Hover states
          },
          'text': {
            'primary': '#f8fafc', // Almost white
            'secondary': '#cbd5e1', // Light gray
            'muted': '#94a3b8', // Muted gray
          },
          'pink': {
            'primary': '#f472b6', // Bright pink for dark mode
            'secondary': '#ec4899', // Slightly darker pink
            'accent': '#be185d', // Dark pink accent
            'border': '#831843', // Dark pink border
            'glow': 'rgba(244, 114, 182, 0.3)', // Pink glow effect
          },
          'border': '#334155', // Dark border color
          'shadow': 'rgba(0, 0, 0, 0.5)', // Dark shadow
        },
      },
      spacing: {
        '18': '4.5rem',
        '25': '6.25rem',
      },
      scale: {
        '200': '2',
      },
      zIndex: {
        '-10': '-10',
        '-3': '-3',
      },
      margin: {
        'clamp-responsive': 'clamp(20px, 10vw, 150px)',
      },
      maxWidth: {
        '1300': '1300px',
        '1250': '1250px',
      },
      boxShadow: {
        'pink': '0px 7px 50px 0px #f9a8d4',
        'card': '0px 0px 20px rgba(0, 0, 0, 0.055)',
        'drop': '0 0 25px rgb(0, 0, 0)',
        // Dark mode shadows
        'dark-card': '0px 0px 25px rgba(0, 0, 0, 0.3)',
        'dark-pink': '0px 7px 50px 0px rgba(244, 114, 182, 0.3)',
        'dark-glow': '0 0 20px rgba(244, 114, 182, 0.2)',
      },
      animation: {
        'slide-in-top': 'slide-in-top 0.3s both',
      },
      keyframes: {
        'slide-in-top': {
          '0%': {
            transform: 'translateY(-50px)',
            opacity: '0',
          },
          '100%': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
      },
    },
  },
  plugins: [],
}
