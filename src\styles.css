@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Color Scheme - Pink/Rose Theme with Dark Mode Support */
:root {
  /* Light Mode Colors */
  /* Primary Colors */
  --primary-pink: #db2777; /* pink-600 */
  --primary-pink-dark: #be185d; /* pink-700 */
  --primary-pink-light: #f9a8d4; /* pink-300 */
  --primary-pink-lighter: #fce7f3; /* pink-100 */
  --primary-pink-lightest: #fdf2f8; /* pink-50 */

  /* Rose Colors */
  --rose-light: #fef7f7; /* rose-50 */
  --rose-lighter: #ffe4e6; /* rose-100 */

  /* Accent Colors */
  --accent-pink: #f472b6; /* pink-400 */
  --border-pink: #fbcfe8; /* pink-200 */
  --border-pink-dark: #f9a8d4; /* pink-300 */

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;

  /* Background Gradients */
  --bg-gradient-start: var(--primary-pink-lightest);
  --bg-gradient-middle: #ffffff;
  --bg-gradient-end: var(--rose-lighter);

  /* Shadow Colors */
  --shadow-pink: rgba(251, 207, 232, 0.5); /* pink-200 with opacity */
  --shadow-default: rgba(0, 0, 0, 0.1);

  /* Text Colors */
  --text-primary: var(--primary-pink);
  --text-secondary: var(--primary-pink-dark);
  --text-muted: #6b7280;
  --text-on-bg: #1f2937;

  /* Border Colors */
  --border-default: #e5e7eb;
  --border-light: #f3f4f6;
}

/* Dark Mode Colors */
.dark {
  /* Dark Background Colors */
  --bg-primary: #0f0f23; /* Very dark blue-purple */
  --bg-secondary: #1a1a2e; /* Dark blue-purple */
  --bg-tertiary: #16213e; /* Medium dark blue */
  --bg-card: #1e1e3f; /* Card background */
  --bg-hover: #252550; /* Hover states */

  /* Dark Text Colors */
  --text-on-bg: #f8fafc; /* Almost white */
  --text-muted: #cbd5e1; /* Light gray */
  --text-secondary-dark: #94a3b8; /* Muted gray */

  /* Dark Mode Pink Colors (brighter for contrast) */
  --primary-pink: #f472b6; /* Bright pink for dark mode */
  --primary-pink-dark: #ec4899; /* Slightly darker pink */
  --primary-pink-light: #fbbf24; /* Golden accent */
  --accent-pink: #be185d; /* Dark pink accent */

  /* Dark Borders */
  --border-default: #334155; /* Dark border color */
  --border-light: #475569; /* Lighter dark border */
  --border-pink: #831843; /* Dark pink border */
  --border-pink-dark: #be185d; /* Darker pink border */

  /* Dark Shadows */
  --shadow-default: rgba(0, 0, 0, 0.5);
  --shadow-pink: rgba(244, 114, 182, 0.3); /* Pink glow effect */

  /* Dark Background Gradients */
  --bg-gradient-start: var(--bg-primary);
  --bg-gradient-middle: var(--bg-secondary);
  --bg-gradient-end: var(--bg-tertiary);

  /* Update text colors for dark mode */
  --text-primary: var(--primary-pink);
  --text-secondary: var(--text-on-bg);
}

/* Global Font and Base Styles */
*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Apply global pink theme colors and prevent horizontal scroll */
html, body {
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100vw;   /* Ensure content doesn't exceed viewport width */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease; /* Smooth theme transitions */
}

body {
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Global utility classes for consistent theming */
.theme-bg-gradient {
  background: linear-gradient(to bottom right, var(--bg-gradient-start), var(--bg-gradient-middle), var(--bg-gradient-end));
  transition: background 0.3s ease;
}

.theme-text-primary {
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.theme-text-secondary {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.theme-text-on-bg {
  color: var(--text-on-bg);
  transition: color 0.3s ease;
}

.theme-border {
  border-color: var(--border-pink);
  transition: border-color 0.3s ease;
}

.theme-bg-light {
  background-color: var(--primary-pink-lighter);
  transition: background-color 0.3s ease;
}

.theme-bg-primary {
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease;
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
  transition: background-color 0.3s ease;
}

.theme-bg-card {
  background-color: var(--bg-card, var(--bg-primary));
  transition: background-color 0.3s ease;
}

.theme-shadow {
  box-shadow: 0 4px 6px -1px var(--shadow-pink);
  transition: box-shadow 0.3s ease;
}

.theme-border-default {
  border-color: var(--border-default);
  transition: border-color 0.3s ease;
}

/* Global font utilities for consistent typography */
.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Ensure all headings use consistent font family */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Ensure all text elements use consistent font family */
p, span, div, a, button, input, textarea, label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Dark mode specific utility classes */
.dark-mode-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure smooth transitions for all interactive elements */
button, a, input, textarea, select {
  transition: all 0.3s ease;
}

/* Dark mode glow effects */
.dark .glow-pink {
  box-shadow: 0 0 20px rgba(244, 114, 182, 0.3);
}

.dark .glow-pink:hover {
  box-shadow: 0 0 30px rgba(244, 114, 182, 0.5);
}

/* Accessibility improvements for dark mode */
.dark *:focus {
  outline: 2px solid #f472b6;
  outline-offset: 2px;
}

/* Ensure proper contrast for text selection in dark mode */
.dark ::selection {
  background-color: rgba(244, 114, 182, 0.3);
  color: #f8fafc;
}
