@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Color Scheme - Pink/Rose Theme with Dark Mode Support */
:root {
  /* Light Mode Colors */
  /* Primary Colors */
  --primary-pink: #db2777; /* pink-600 */
  --primary-pink-dark: #be185d; /* pink-700 */
  --primary-pink-light: #f9a8d4; /* pink-300 */
  --primary-pink-lighter: #fce7f3; /* pink-100 */
  --primary-pink-lightest: #fdf2f8; /* pink-50 */

  /* Rose Colors */
  --rose-light: #fef7f7; /* rose-50 */
  --rose-lighter: #ffe4e6; /* rose-100 */

  /* Accent Colors */
  --accent-pink: #f472b6; /* pink-400 */
  --border-pink: #fbcfe8; /* pink-200 */
  --border-pink-dark: #f9a8d4; /* pink-300 */

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;

  /* Background Gradients */
  --bg-gradient-start: var(--primary-pink-lightest);
  --bg-gradient-middle: #ffffff;
  --bg-gradient-end: var(--rose-lighter);

  /* Shadow Colors */
  --shadow-pink: rgba(251, 207, 232, 0.5); /* pink-200 with opacity */
  --shadow-default: rgba(0, 0, 0, 0.1);

  /* Text Colors */
  --text-primary: var(--primary-pink);
  --text-secondary: var(--primary-pink-dark);
  --text-muted: #6b7280;
  --text-on-bg: #1f2937;

  /* Border Colors */
  --border-default: #e5e7eb;
  --border-light: #f3f4f6;
}

/* Dark Mode Colors */
.dark {
  /* Dark Background Colors - Much Darker/Black */
  --bg-primary: #000000; /* Pure black background */
  --bg-secondary: #0A0A0A; /* Very dark black */
  --bg-tertiary: #111111; /* Slightly lighter black */
  --bg-card: #2C2C34; /* Keep cards as dark grey-blue for contrast */
  --bg-hover: #1A1A1A; /* Dark hover state */

  /* Dark Text Colors - Your Custom Palette */
  --text-on-bg: #FFFFFF; /* White text */
  --text-muted: #CCCCCC; /* Soft grey for supporting text */
  --text-secondary-dark: #CCCCCC; /* Soft grey for supporting text */

  /* Dark Mode Pink Colors - Your Custom Palette */
  --primary-pink: #F48FB1; /* Soft pink for accents & buttons */
  --primary-pink-dark: #6A4F58; /* Muted mauve for variety */
  --primary-pink-light: #FFB6C1; /* Light pink for hover effect */
  --accent-pink: #F48FB1; /* Soft pink for accents */

  /* Dark Borders */
  --border-default: #3A3A42; /* Slightly lighter than surface */
  --border-light: #4A4A52; /* Even lighter for subtle borders */
  --border-pink: #6A4F58; /* Muted mauve for pink borders */
  --border-pink-dark: #F48FB1; /* Soft pink for active borders */

  /* Dark Shadows */
  --shadow-default: rgba(0, 0, 0, 0.5);
  --shadow-pink: rgba(244, 143, 177, 0.3); /* Soft pink glow effect */

  /* Dark Background Gradients */
  --bg-gradient-start: var(--bg-primary);
  --bg-gradient-middle: var(--bg-secondary);
  --bg-gradient-end: var(--bg-tertiary);

  /* Update text colors for dark mode */
  --text-primary: var(--primary-pink);
  --text-secondary: var(--text-on-bg);
}

/* Global Font and Base Styles */
*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Apply global pink theme colors and prevent horizontal scroll */
html, body {
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-width: 100vw;   /* Ensure content doesn't exceed viewport width */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease; /* Smooth theme transitions */
}

body {
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Global utility classes for consistent theming */
.theme-bg-gradient {
  background: linear-gradient(to bottom right, var(--bg-gradient-start), var(--bg-gradient-middle), var(--bg-gradient-end));
  transition: background 0.3s ease;
}

.theme-text-primary {
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.theme-text-secondary {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.theme-text-on-bg {
  color: var(--text-on-bg);
  transition: color 0.3s ease;
}

.theme-border {
  border-color: var(--border-pink);
  transition: border-color 0.3s ease;
}

.theme-bg-light {
  background-color: var(--primary-pink-lighter);
  transition: background-color 0.3s ease;
}

.theme-bg-primary {
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease;
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
  transition: background-color 0.3s ease;
}

.theme-bg-card {
  background-color: var(--bg-card, var(--bg-primary));
  transition: background-color 0.3s ease;
}

.theme-shadow {
  box-shadow: 0 4px 6px -1px var(--shadow-pink);
  transition: box-shadow 0.3s ease;
}

.theme-border-default {
  border-color: var(--border-default);
  transition: border-color 0.3s ease;
}

/* Global font utilities for consistent typography */
.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Ensure all headings use consistent font family */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Ensure all text elements use consistent font family */
p, span, div, a, button, input, textarea, label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Dark mode specific utility classes */
.dark-mode-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure smooth transitions for all interactive elements */
button, a, input, textarea, select {
  transition: all 0.3s ease;
}

/* Dark mode glow effects - Updated for your custom palette */
.dark .glow-pink {
  box-shadow: 0 0 20px rgba(244, 143, 177, 0.3); /* Soft pink glow */
}

.dark .glow-pink:hover {
  box-shadow: 0 0 30px rgba(255, 182, 193, 0.5); /* Light pink hover glow */
}

/* Accessibility improvements for dark mode */
.dark *:focus {
  outline: 2px solid #F48FB1; /* Soft pink focus outline */
  outline-offset: 2px;
}

/* Ensure proper contrast for text selection in dark mode */
.dark ::selection {
  background-color: rgba(244, 143, 177, 0.3); /* Soft pink selection */
  color: #FFFFFF; /* White text */
}

/* Additional utility classes for your custom dark mode palette */
.dark .bg-dark-primary {
  background-color: #000000; /* Pure black background */
}

.dark .bg-dark-surface {
  background-color: #2C2C34; /* Dark grey-blue for depth */
}

.dark .text-dark-primary {
  color: #FFFFFF; /* White text */
}

.dark .text-dark-secondary {
  color: #CCCCCC; /* Soft grey for supporting text */
}

.dark .text-pink-primary {
  color: #F48FB1; /* Soft pink for accents */
}

.dark .text-pink-secondary {
  color: #6A4F58; /* Muted mauve for variety */
}

.dark .border-dark-surface {
  border-color: #3A3A42; /* Slightly lighter than surface */
}

.dark .hover\:bg-pink-hover:hover {
  background-color: #FFB6C1; /* Light pink for hover effect */
}

.dark .hover\:text-pink-hover:hover {
  color: #FFB6C1; /* Light pink for hover effect */
}
