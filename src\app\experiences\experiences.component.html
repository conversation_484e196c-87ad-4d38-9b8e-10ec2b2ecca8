<div class="pt-10 transition-colors duration-300" id="experience">
  <div class="text-center pb-8">
    <h2 class="text-4xl font-righteous uppercase theme-text-primary mb-2 relative inline-block after:block after:w-24 after:h-1 after:bg-pink-400 dark:after:bg-pink-500 after:rounded after:mx-auto after:mt-2 transition-colors duration-300">
      Experience
    </h2>
  </div>

  <div class="relative max-w-5xl mx-auto px-4">

    <div class="absolute left-1/2 transform -translate-x-1/2 h-full border-l-2 border-pink-400 dark:border-pink-500 transition-colors duration-300"></div>

    <div *ngFor="let exp of experiences; let i = index" class="mb-16 flex flex-col sm:flex-row items-center w-full relative">


      <div class="w-full sm:w-1/2 pr-4 sm:pr-8 flex justify-end z-10">
        <div *ngIf="i % 2 === 0" class="bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border p-6 rounded-lg shadow dark:shadow-dark-card w-full max-w-md text-right transition-all duration-300">
          <h3 class="text-xl font-semibold theme-text-secondary transition-colors duration-300">{{ exp.title }}</h3>
          <p class="text-pink-600 dark:text-pink-400 transition-colors duration-300">{{ exp.company }}</p>
          <p class="text-pink-500 dark:text-pink-300 text-sm transition-colors duration-300">{{ exp.from | date:'MMM yyyy' }} - {{ exp.to ? (exp.to | date:'MMM yyyy') : 'Present' }}</p>
        </div>
      </div>


      <div class="absolute left-1/2 transform -translate-x-1/2 w-5 h-5 bg-pink-400 dark:bg-pink-500 rounded-full border-4 border-white dark:border-dark-bg-primary shadow z-20 transition-all duration-300"></div>

      <div class="w-full sm:w-1/2 pl-4 sm:pl-8 flex justify-start z-10">
        <div *ngIf="i % 2 !== 0" class="bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border p-6 rounded-lg shadow dark:shadow-dark-card w-full max-w-md text-left transition-all duration-300">
          <h3 class="text-xl font-semibold theme-text-secondary transition-colors duration-300">{{ exp.title }}</h3>
          <p class="text-pink-600 dark:text-pink-400 transition-colors duration-300">{{ exp.company }}</p>
          <p class="text-pink-500 dark:text-pink-300 text-sm transition-colors duration-300">{{ exp.from | date:'MMM yyyy' }} - {{ exp.to ? (exp.to | date:'MMM yyyy') : 'Present' }}</p>
        </div>
      </div>
    </div>
  </div>
</div>
