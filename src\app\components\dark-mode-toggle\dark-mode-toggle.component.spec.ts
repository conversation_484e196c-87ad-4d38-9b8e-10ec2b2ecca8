import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DarkModeToggleComponent } from './dark-mode-toggle.component';
import { DarkModeService } from '../../services/dark-mode.service';
import { BehaviorSubject } from 'rxjs';

describe('DarkModeToggleComponent', () => {
  let component: DarkModeToggleComponent;
  let fixture: ComponentFixture<DarkModeToggleComponent>;
  let mockDarkModeService: jasmine.SpyObj<DarkModeService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('DarkModeService', ['toggleDarkMode'], {
      isDarkMode$: new BehaviorSubject(false)
    });

    await TestBed.configureTestingModule({
      declarations: [DarkModeToggleComponent],
      providers: [
        { provide: DarkModeService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DarkModeToggleComponent);
    component = fixture.componentInstance;
    mockDarkModeService = TestBed.inject(DarkModeService) as jasmine.SpyObj<DarkModeService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle dark mode when clicked', () => {
    component.onToggle();
    expect(mockDarkModeService.toggleDarkMode).toHaveBeenCalled();
  });

  it('should update isDarkMode when service emits', () => {
    const darkModeSubject = mockDarkModeService.isDarkMode$ as BehaviorSubject<boolean>;
    
    darkModeSubject.next(true);
    expect(component.isDarkMode).toBe(true);
    
    darkModeSubject.next(false);
    expect(component.isDarkMode).toBe(false);
  });
});
