

.hero-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;                    /* Ensure full viewport width */
  max-width: 100%;                 /* Prevent overflow */
  color: var(--text-primary);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--bg-primary);
}

.hero-section:hover {
  /* Removed scale transform to prevent horizontal overflow */
  filter: brightness(1.05);       /* Alternative hover effect */
}

.profile-pic {
  border-radius: 50%;
  overflow: hidden;
  width: 150px;
  height: 150px;
  margin-bottom: 20px;
  border: 4px solid var(--border-pink);
  box-shadow: 0 0 20px var(--shadow-pink);
  transition: all 0.3s ease;
}

/* Dark mode styles */
:host-context(.dark) .profile-pic {
  border-color: var(--border-pink);
  box-shadow: 0 0 25px var(--shadow-pink);
}

.profile-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px var(--shadow-pink);
  transition: all 0.3s ease;
}

.hero-section p {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

/* Dark mode text styles */
:host-context(.dark) .hero-section h1 {
  color: var(--text-primary);
  text-shadow: 2px 2px 8px var(--shadow-pink);
}

:host-context(.dark) .hero-section p {
  color: var(--text-on-bg);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  color: var(--text-primary);
  font-size: 1.5rem;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: var(--accent-pink);
}

/* Dark mode social icons */
:host-context(.dark) .social-icons a {
  color: var(--text-primary);
}

:host-context(.dark) .social-icons a:hover {
  color: var(--accent-pink);
  filter: drop-shadow(0 0 8px var(--shadow-pink));
}

/* Vanta.js background will be automatically applied to #vanta-birds */


