<div class="text-center mt-10 transition-colors duration-300" [@fadeInUp]>
  <h2 class="text-5xl font-righteous uppercase text-pink-600 dark:text-pink-400 mb-10 relative transition-colors duration-300">
    Projects
    <span class="block w-24 h-1 bg-pink-400 dark:bg-pink-500 mt-2 mx-auto rounded transition-colors duration-300"></span>
  </h2>
</div>

<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10 px-6" [@staggerAnimation]>
  <div *ngFor="let project of projects; let i = index"
    class="project-card bg-white dark:bg-dark-bg-card border border-gray-200 dark:border-dark-border rounded-2xl p-6 shadow-lg dark:shadow-dark-card flex flex-col justify-between fade-in-up transition-all duration-300"
    [@cardHover]="getCardState(i)"
    (mouseenter)="onCardHover(i)"
    (mouseleave)="onCardLeave()"
    [@fadeInUp]>

    <div class="overflow-hidden rounded-xl mb-4 h-52 relative">
      <img [src]="project.photo" alt="Project Image"
        class="project-image w-full h-full object-cover" />
      <div class="absolute inset-0 bg-gradient-to-t from-pink-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    </div>

    <div class="text-gray-800 dark:text-gray-200 transition-colors duration-300">
      <h3 class="title-underline text-2xl font-bold font-righteous uppercase text-gray-900 dark:text-gray-100 transition-colors duration-300">{{ project.name }}</h3>
      <p class="text-sm font-lato uppercase tracking-wide text-pink-600 dark:text-pink-400 mt-1 transition-colors duration-300">{{ project.title }}</p>
      <p class="text-sm mt-2 text-gray-700 dark:text-gray-300 transition-colors duration-300">{{ project.description }}</p>

      <div class="mt-4">
        <strong class="text-pink-600 dark:text-pink-400 transition-colors duration-300">Skills:</strong>
        <div class="flex flex-wrap gap-2 mt-1">
          <span *ngFor="let skill of project.skills"
            class="skill-tag bg-pink-100 dark:bg-dark-bg-hover text-pink-700 dark:text-pink-300 text-xs font-semibold px-2 py-1 rounded-full cursor-default transition-all duration-300">
            {{ skill }}
          </span>
        </div>
      </div>
    </div>

    <div class="mt-6 flex justify-between gap-3">
      <a [href]="project.githubLink" target="_blank"
        class="project-button text-xs uppercase px-4 py-2 border border-pink-500 dark:border-pink-400 text-pink-500 dark:text-pink-400 rounded-full hover:bg-pink-500 dark:hover:bg-pink-400 hover:text-white dark:hover:text-gray-900 flex-1 text-center transition-all duration-300">
        GitHub
      </a>
      <a [href]="project.link" target="_blank"
        class="project-button text-xs uppercase px-4 py-2 border border-pink-500 dark:border-pink-400 text-pink-500 dark:text-pink-400 rounded-full hover:bg-pink-500 dark:hover:bg-pink-400 hover:text-white dark:hover:text-gray-900 flex-1 text-center transition-all duration-300">
        Live Demo
      </a>
    </div>
  </div>
</div>
